# Rust Repo Map CLI Tool - Progress Tracker

## Current Status: Phase 1 - Basic CLI ✅

## Completed Tasks

### Investigation Phase ✅
- [x] Analyzed Python tree-sitter repo map implementation in aider
- [x] Identified key CLI parameters: --map-tokens, --map-refresh, --map-multiplier-no-files, --verbose
- [x] Understood tree-sitter query system and symbol extraction
- [x] Examined output format from test fixtures
- [x] Analyzed symbol ranking and tree rendering logic
- [x] Created comprehensive implementation plan

### Key Findings from Investigation:
- Python version uses tree-sitter with .scm query files for each language
- Output format uses Unicode box-drawing characters (│, ⋮)
- Symbol ranking based on cross-references and importance
- Token counting with binary search to fit within limits
- Supports refresh strategies: auto, always, files, manual
- Uses grep-ast TreeContext for rendering code snippets

## Next Steps

### Phase 1: Project Setup and Basic CLI ✅
- [x] Create new Rust project with Cargo
- [x] Add dependencies (clap, tree-sitter, walkdir, etc.)
- [x] Implement CLI argument parsing
- [x] Basic file discovery functionality
- [x] Test with simple file listing

### Phase 2: Tree-sitter Integration ✅
- [x] Add tree-sitter Rust language support
- [x] Copy and adapt tree-sitter query files from Python version
- [x] Implement basic symbol extraction for Rust files
- [x] Create simple symbol data structures
- [x] Test symbol extraction with sample Rust code
- [x] Add support for Python, JavaScript, Go languages
- [x] Test symbol extraction with Python files

### Phase 3: Core Functionality
- [ ] Implement symbol ranking algorithm
- [ ] Create tree rendering with proper Unicode formatting
- [ ] Add token counting functionality
- [ ] Implement binary search for optimal symbol selection
- [ ] Test output format matches expected structure

### Phase 4: Multi-language Support
- [ ] Add Python language support
- [ ] Add JavaScript/TypeScript support
- [ ] Add Go language support
- [ ] Implement dynamic query file loading
- [ ] Test with various programming languages

### Phase 5: Advanced Features
- [ ] Implement all refresh strategies
- [ ] Add caching for performance
- [ ] Add verbose output and debugging
- [ ] Performance optimization
- [ ] Error handling and edge cases

## Technical Decisions Made

### CLI Interface Design:
- Use `--size` instead of `--map-tokens` for brevity
- Keep other parameter names consistent with Python version
- Support both files and directories as positional arguments

### Architecture:
- Modular design with separate modules for each major component
- Use anyhow for error handling
- Store tree-sitter queries in embedded resources
- Use walkdir for efficient directory traversal

### Dependencies Selected:
- clap 4.x for modern CLI parsing
- tree-sitter with language-specific crates
- walkdir for directory traversal
- ignore for .gitignore support
- anyhow for error handling

## Challenges Identified

1. **Tree-sitter Query Compatibility**: Need to ensure Rust tree-sitter queries work the same as Python version
2. **Token Counting**: Need to implement accurate token counting that matches Python tiktoken
3. **Symbol Ranking**: Complex algorithm that needs to match Python behavior
4. **Unicode Rendering**: Proper handling of box-drawing characters
5. **Performance**: Large codebases need efficient processing

## Testing Plan

### Unit Tests:
- Symbol extraction for each language
- Token counting accuracy
- Tree rendering format
- File discovery and filtering

### Integration Tests:
- Compare output with Python version on same codebase
- Test with aider's own codebase
- Performance benchmarks

### Test Data:
- Use existing test fixtures from Python version
- Create additional test cases for edge cases
- Test with various programming languages

## Success Metrics

- [ ] CLI interface fully functional
- [ ] Output format matches Python version
- [ ] Supports Rust, Python, JavaScript, Go
- [ ] Performance acceptable for large codebases (< 5 seconds for aider codebase)
- [ ] Token counting within 5% of Python version
- [ ] All CLI parameters working correctly

## Notes

- Python version is in `/Users/<USER>/Developer/aider_orig/aider/repomap.py`
- Tree-sitter queries are in `/Users/<USER>/Developer/aider_orig/aider/queries/`
- Test fixtures available in `/Users/<USER>/Developer/aider_orig/tests/fixtures/`
- Expected output format documented in `sample-code-base-repo-map.txt`
