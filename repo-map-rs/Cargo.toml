[package]
name = "repo-map-rs"
version = "0.1.0"
edition = "2024"

[dependencies]
clap = { version = "4.4", features = ["derive"] }
tree-sitter = "0.20"
tree-sitter-rust = "0.20"
tree-sitter-python = "0.20"
tree-sitter-javascript = "0.20"
tree-sitter-go = "0.20"
walkdir = "2.4"
ignore = "0.4"
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
regex = "1.10"

[dev-dependencies]
tempfile = "3.8"
