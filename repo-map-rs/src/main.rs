use clap::Parser;
use std::path::PathBuf;
use anyhow::Result;
use repo_map_rs::{FileDiscovery, parse_file, Symbol};
use std::fs;

#[derive(Parser)]
#[command(name = "repo-map")]
#[command(about = "Generate a repository map showing code structure and symbols")]
#[command(version)]
struct Args {
    /// Token limit for the output (similar to --map-tokens in aider)
    #[arg(long, default_value = "1024")]
    size: usize,

    /// Control how often the repo map is refreshed
    #[arg(long, default_value = "auto")]
    #[arg(value_parser = ["auto", "always", "files", "manual"])]
    refresh: String,

    /// Multiplier for map tokens when no files are specified
    #[arg(long, default_value = "2.0")]
    multiplier_no_files: f64,

    /// Enable verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Root directory to analyze
    #[arg(long, default_value = ".")]
    root: PathBuf,

    /// Files or directories to analyze
    files: Vec<PathBuf>,
}

fn main() -> Result<()> {
    let args = Args::parse();

    if args.verbose {
        eprintln!("Repo map configuration:");
        eprintln!("  Size: {} tokens", args.size);
        eprintln!("  Refresh: {}", args.refresh);
        eprintln!("  Multiplier: {}", args.multiplier_no_files);
        eprintln!("  Root: {}", args.root.display());
        eprintln!("  Files: {:?}", args.files);
    }

    // Initialize file discovery
    let file_discovery = FileDiscovery::new(args.root.clone());

    // Find source files
    let files = file_discovery.find_source_files(&args.files)?;

    if args.verbose {
        eprintln!("Found {} source files:", files.len());
        for file in &files {
            if let Some(lang) = file_discovery.get_language(file) {
                eprintln!("  {} ({})", file.display(), lang);
            } else {
                eprintln!("  {} (unknown)", file.display());
            }
        }
    }

    // Parse files and extract symbols
    let mut all_symbols = Vec::new();

    for file in &files {
        if let Some(language) = file_discovery.get_language(file) {
            match fs::read_to_string(file) {
                Ok(content) => {
                    match parse_file(file, language, &content) {
                        Ok(symbols) => {
                            if args.verbose {
                                eprintln!("  Found {} symbols in {}", symbols.len(), file.display());
                            }
                            all_symbols.extend(symbols);
                        }
                        Err(e) => {
                            if args.verbose {
                                eprintln!("  Failed to parse {}: {}", file.display(), e);
                            }
                        }
                    }
                }
                Err(e) => {
                    if args.verbose {
                        eprintln!("  Failed to read {}: {}", file.display(), e);
                    }
                }
            }
        }
    }

    if args.verbose {
        eprintln!("Total symbols found: {}", all_symbols.len());
    }

    // TODO: Implement symbol ranking and tree rendering
    println!("Found {} symbols across {} files", all_symbols.len(), files.len());

    // Show a sample of symbols for now
    for symbol in all_symbols.iter().take(10) {
        println!("{:?} {} at line {} in {}",
                 symbol.kind,
                 symbol.name,
                 symbol.line,
                 symbol.file_path.display());
    }

    Ok(())
}
