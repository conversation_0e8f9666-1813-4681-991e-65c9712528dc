{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 1369601567987815722, "path": 15886139403474195683, "deps": [[1988483478007900009, "unicode_ident", false, 2095044605240813637], [3060637413840920116, "proc_macro2", false, 14950412238561819026], [17990358020177143287, "quote", false, 18230797021385217326]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-2ed64cffe55a2c7c/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}