{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 12678764947066685445, "deps": [[4684437522915235464, "libc", false, 7193656306221092954], [7896293946984509699, "bitflags", false, 16027520723241446092], [8253628577145923712, "libc_errno", false, 8108034401719925862], [10004434995811528692, "build_script_build", false, 3928271518463402034]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-75338fed464a406b/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}