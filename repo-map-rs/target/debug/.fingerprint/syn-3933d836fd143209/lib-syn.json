{"rustc": 12610991425282158916, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 15886139403474195683, "deps": [[1988483478007900009, "unicode_ident", false, 17194228137114969474], [3060637413840920116, "proc_macro2", false, 17229471756647568815], [17990358020177143287, "quote", false, 5601385305868493628]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-3933d836fd143209/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}