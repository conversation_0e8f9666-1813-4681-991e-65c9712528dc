# Copyright 2014 Google Inc. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""A pure Python implementation of the JSON5 configuration language."""

from json5.lib import JSON5Encoder, QuoteStyle, load, loads, dump, dumps
from json5.version import __version__, VERSION


__all__ = [
    'JSON5Encoder',
    'QuoteStyle',
    'VERSION',
    '__version__',
    'dump',
    'dumps',
    'load',
    'loads',
]
